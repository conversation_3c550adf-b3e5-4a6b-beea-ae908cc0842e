<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Certificate - {{ candidate.Name }}</title>
    <link href="https://fonts.googleapis.com/css2?family=Playfair+Display:wght@400;600;700;900&family=Inter:wght@300;400;500;600;700&family=Crimson+Text:ital,wght@0,400;0,600;1,400&display=swap" rel="stylesheet">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            margin: 0;
            padding: 20px;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            font-family: 'Inter', sans-serif;
            display: flex;
            justify-content: center;
            align-items: center;
            min-height: 100vh;
        }

        .certificate {
            width: 794px;
            height: 1123px;
            background: linear-gradient(145deg, #ffffff 0%, #f8f9fa 100%);
            margin: 0 auto;
            padding: 0;
            position: relative;
            box-shadow:
                0 25px 50px rgba(0,0,0,0.15),
                0 15px 35px rgba(0,0,0,0.1),
                inset 0 1px 0 rgba(255,255,255,0.6);
            box-sizing: border-box;
            border-radius: 8px;
            overflow: hidden;
        }

        /* Elegant border design */
        .certificate::before {
            content: '';
            position: absolute;
            top: 20px;
            left: 20px;
            right: 20px;
            bottom: 20px;
            border: 3px solid transparent;
            background: linear-gradient(45deg, #004a77, #1f5d8b, #004a77) border-box;
            border-radius: 6px;
            z-index: 1;
        }

        .certificate::after {
            content: '';
            position: absolute;
            top: 30px;
            left: 30px;
            right: 30px;
            bottom: 30px;
            border: 1px solid rgba(0,74,119,0.2);
            border-radius: 4px;
            z-index: 1;
        }

        .certificate-content {
            position: relative;
            z-index: 2;
            padding: 50px;
            height: 100%;
            display: flex;
            flex-direction: column;
        }

        .header-container {
            text-align: center;
            margin-bottom: 35px;
        }

        .logo-img {
            width: 340px;
            height: auto;
            max-width: 100%;
            filter: drop-shadow(0 2px 4px rgba(0,0,0,0.1));
        }

        .title {
            text-align: center;
            font-size: 34px;
            font-weight: bold;
            font-style: italic;
            margin: 45px 0 20px;
            color: #2c3e50;
            letter-spacing: 3px;
            text-transform: uppercase;
            text-shadow: 1px 1px 2px rgba(0,0,0,0.1);
            position: relative;
        }

        .title::after {
            content: '';
            position: absolute;
            bottom: -10px;
            left: 50%;
            transform: translateX(-50%);
            width: 200px;
            height: 3px;
            background: linear-gradient(90deg, transparent, #004a77, transparent);
        }

        .subtitle {
            text-align: center;
            font-size: 20px;
            margin-bottom: 30px;
            color: #34495e;
            font-style: italic;
            font-weight: 300;
        }

        .name {
            text-align: center;
            font-size: 44px;
            font-weight: bold;
            color: #1f5d8b;
            margin: 35px 0;
            text-transform: uppercase;
            letter-spacing: 4px;
            text-shadow: 2px 2px 4px rgba(0,0,0,0.1);
            position: relative;
            padding: 15px 0;
        }

        .name::before,
        .name::after {
            content: '';
            position: absolute;
            left: 50%;
            transform: translateX(-50%);
            width: 300px;
            height: 2px;
            background: linear-gradient(90deg, transparent, #1f5d8b, transparent);
        }

        .name::before {
            top: 0;
        }

        .name::after {
            bottom: 0;
        }

        .project {
            text-align: center;
            font-size: 18px;
            margin-bottom: 15px;
            line-height: 1.8;
            color: #2c3e50;
            padding: 0 40px;
        }

        .project-name {
            font-weight: bold;
            color: #004a77;
            font-size: 22px;
            display: inline-block;
            padding: 8px 20px;
            background: rgba(0,74,119,0.05);
            border-radius: 8px;
            margin: 10px 0;
            border: 2px solid rgba(0,74,119,0.1);
        }

        .firm {
            text-align: center;
            font-size: 17px;
            margin-bottom: 45px;
            line-height: 2;
            color: #2c3e50;
            padding: 0 30px;
        }

        .firm-name {
            font-weight: bold;
            color: #004a77;
            font-size: 18px;
        }

        .date-range {
            font-weight: 600;
            color: #1f5d8b;
        }

        .seal {
            text-align: center;
            margin-top: 35px;
        }

        .seal-img {
            width: 160px;
            height: auto;
            filter: drop-shadow(0 2px 4px rgba(0,0,0,0.1));
        }

        .code {
            text-align: center;
            font-size: 18px;
            margin-top: 25px;
            color: #1f5d8b;
            font-weight: bold;
            background: linear-gradient(135deg, #f8f9fa, #e9ecef);
            padding: 12px 20px;
            border-radius: 8px;
            border: 2px solid #dee2e6;
            display: inline-block;
            box-shadow: 0 2px 4px rgba(0,0,0,0.1);
            font-family: 'Courier New', monospace;
            letter-spacing: 1px;
        }

        .footer {
            position: absolute;
            bottom: 60px;
            left: 50px;
            right: 50px;
            display: flex;
            justify-content: space-between;
            align-items: flex-end;
            font-size: 14px;
        }

        .footer .date {
            text-align: center;
            width: 200px;
        }

        .footer .issued-date {
            display: inline-block;
            border-top: 3px solid #333;
            padding-top: 10px;
            margin-bottom: 8px;
            font-weight: bold;
            min-width: 140px;
            font-size: 15px;
        }

        .footer .date-label {
            font-size: 16px;
            color: #555;
            font-weight: bold;
            margin-bottom: 15px;
        }

        .footer .sign {
            text-align: center;
            width: 220px;
        }

        .sign-img {
            width: 180px;
            margin-bottom: 8px;
            filter: drop-shadow(0 1px 2px rgba(0,0,0,0.1));
        }

        .sign-name {
            display: inline-block;
            border-top: 3px solid #333;
            padding-top: 8px;
            font-weight: bold;
            min-width: 160px;
            font-size: 15px;
        }

        .sign-title {
            font-size: 13px;
            color: #555;
            margin-top: 4px;
            font-weight: 600;
        }

        /* Decorative elements */
        .certificate::before {
            content: '';
            position: absolute;
            top: 20px;
            left: 20px;
            right: 20px;
            bottom: 20px;
            border: 2px solid rgba(0,74,119,0.1);
            border-radius: 4px;
            pointer-events: none;
        }

        /* Print styles */
        @media print {
            body {
                background: white;
                padding: 0;
            }
            .certificate {
                box-shadow: none;
                margin: 0;
                page-break-inside: avoid;
            }
        }

        /* Responsive adjustments */
        @media (max-width: 850px) {
            .certificate {
                width: 90%;
                height: auto;
                min-height: 1123px;
                padding: 40px 30px;
            }
            
            .title {
                font-size: 28px;
                letter-spacing: 2px;
            }
            
            .name {
                font-size: 36px;
                letter-spacing: 3px;
            }
            
            .project, .firm {
                padding: 0 20px;
            }
            
            .logo-img {
                width: 280px;
            }
        }
    </style>
</head>
<body>
    <div class="certificate" id="certificate">
        <div class="header-container">
            <img src="{{ url_for('static', filename='logo.png') }}" alt="Innodatatics Logo" class="logo-img">
        </div>

        <div class="title">Certificate of Completion</div>
        <div class="subtitle">This certificate is presented to</div>

        <div class="name">{{ candidate.Name }}</div>

        <div class="project">
            for successfully completing the Internship Project on
            <br><br>
            <span class="project-name">"{{ candidate.Project }}"</span>
        </div>

        <div class="firm">
            through emerging technologies consulting firm
            <br><br>
            <span class="firm-name">"Innodatatics - USA"</span>
            <br>
            from <span class="date-range">{{ start_date }}</span> to <span class="date-range">{{ end_date }}</span>
        </div>

        <div class="seal">
            <img src="{{ url_for('static', filename='badges.png') }}" alt="Seal Badge" class="seal-img">
            <br>
            <div class="code">Certificate Code: {{ candidate.CertificateCode }}</div>
        </div>

        <div class="footer">
            <div class="date">
                <div class="date-label">Date</div>
                <div class="issued-date">{{ issue_date }}</div>
            </div>
            <div class="sign">
                <img src="{{ url_for('static', filename='signature.png') }}" alt="Signature" class="sign-img">
                <div class="sign-name">Shirish G Kumar</div>
                <div class="sign-title">CEO & MD</div>
            </div>
        </div>
    </div>
</body>
</html>
