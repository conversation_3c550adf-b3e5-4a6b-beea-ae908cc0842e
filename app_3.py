from flask import Flask, render_template, request, redirect, url_for, flash, jsonify
import os
from datetime import datetime
from config import config, get_email_config
from utils.excel_manager import ExcelManager
from utils.certificate_generator import CertificateGenerator
from utils.email_service import EmailService
from utils.code_generator import CertificateCodeGenerator

def create_app(config_name=None):
    app = Flask(__name__)

    # Load configuration
    config_name = config_name or os.getenv('FLASK_CONFIG', 'default')
    app.config.from_object(config[config_name])
    config[config_name].init_app(app)

    # Initialize utilities
    excel_manager = ExcelManager(app.config['EXCEL_FILE_PATH'])
    certificate_generator = CertificateGenerator()
    email_config = get_email_config()
    email_service = EmailService() if email_config['configured'] else None

    # Main routes
    @app.route('/')
    def index():
        candidates = excel_manager.read_all_candidates()
        total_candidates = len(candidates)
        certificates_generated = sum(1 for c in candidates if c.get('CertificateCode'))
        emails_configured = 1 if email_config['configured'] else 0
        recent_certificates = sum(1 for c in candidates if c.get('IssueDate'))  # Simplified

        return render_template('index.html',
                             candidates=candidates,
                             total_candidates=total_candidates,
                             certificates_generated=certificates_generated,
                             emails_configured=emails_configured,
                             recent_certificates=recent_certificates)

    @app.route('/add_candidate', methods=['GET', 'POST'])
    def add_candidate():
        if request.method == 'POST':
            candidate_data = {
                'Name': request.form.get('name', '').strip(),
                'Email': request.form.get('email', '').strip(),
                'Project': request.form.get('project', '').strip(),
                'StartDate': request.form.get('start_date', '').strip(),
                'EndDate': request.form.get('end_date', '').strip(),
                'IssueDate': request.form.get('issue_date', '').strip()
            }

            # Validate data
            errors = excel_manager.validate_candidate_data(candidate_data)
            if errors:
                for error in errors:
                    flash(error, 'error')
                return render_template('add_candidate.html', candidate=candidate_data)

            # Add candidate
            if excel_manager.add_candidate(candidate_data):
                flash('Candidate added successfully!', 'success')
                return redirect(url_for('index'))
            else:
                flash('Error adding candidate. Please try again.', 'error')

        return render_template('add_candidate.html')

    @app.route('/edit_candidate/<int:index>', methods=['GET', 'POST'])
    def edit_candidate(index):
        candidate = excel_manager.get_candidate_by_index(index)
        if not candidate:
            flash('Candidate not found!', 'error')
            return redirect(url_for('index'))

        if request.method == 'POST':
            candidate_data = {
                'Name': request.form.get('name', '').strip(),
                'Email': request.form.get('email', '').strip(),
                'Project': request.form.get('project', '').strip(),
                'StartDate': request.form.get('start_date', '').strip(),
                'EndDate': request.form.get('end_date', '').strip(),
                'IssueDate': request.form.get('issue_date', '').strip()
            }

            # Validate data
            errors = excel_manager.validate_candidate_data(candidate_data)
            if errors:
                for error in errors:
                    flash(error, 'error')
                return render_template('add_candidate.html',
                                     candidate=candidate_data,
                                     edit_mode=True,
                                     candidate_index=index)

            # Update candidate
            if excel_manager.update_candidate(index, candidate_data):
                flash('Candidate updated successfully!', 'success')
                return redirect(url_for('index'))
            else:
                flash('Error updating candidate. Please try again.', 'error')

        return render_template('add_candidate.html',
                             candidate=candidate,
                             edit_mode=True,
                             candidate_index=index)

    @app.route('/manage_candidates')
    def manage_candidates():
        candidates = excel_manager.read_all_candidates()
        return render_template('manage_candidates.html', candidates=candidates)

    @app.route('/bulk_operations')
    def bulk_operations():
        candidates = excel_manager.read_all_candidates()
        candidates_with_email = sum(1 for c in candidates if c.get('Email'))
        candidates_missing_codes = sum(1 for c in candidates if not c.get('CertificateCode'))

        return render_template('bulk_operations.html',
                             candidates=candidates,
                             candidates_with_email=candidates_with_email,
                             candidates_missing_codes=candidates_missing_codes,
                             email_configured=email_config['configured'],
                             gmail_user=email_config.get('gmail_user', ''))

    # API routes
    @app.route('/api/generate_codes', methods=['POST'])
    def api_generate_codes():
        try:
            result = excel_manager.regenerate_all_certificate_codes()
            if result is not None:
                return jsonify({'success': True, 'message': 'Certificate codes generated successfully'})
            else:
                return jsonify({'success': False, 'message': 'Error generating codes'})
        except Exception as e:
            return jsonify({'success': False, 'message': str(e)})

    @app.route('/api/certificate_preview/<int:index>')
    def api_certificate_preview(index):
        try:
            candidate = excel_manager.get_candidate_by_index(index)
            if not candidate:
                return jsonify({'success': False, 'message': 'Candidate not found'})

            preview_data = certificate_generator.create_certificate_preview_data(candidate)
            html = certificate_generator.generate_certificate_html(candidate)

            return jsonify({
                'success': True,
                'html': html,
                'candidate': preview_data
            })
        except Exception as e:
            return jsonify({'success': False, 'message': str(e)})

    @app.route('/api/preview_certificate', methods=['POST'])
    def api_preview_certificate():
        try:
            candidate_data = request.get_json()

            # Generate certificate code if not provided
            if not candidate_data.get('CertificateCode'):
                code_generator = CertificateCodeGenerator()
                candidate_data['CertificateCode'] = code_generator.generate_certificate_code(
                    candidate_data.get('start_date', '')
                )

            html = certificate_generator.generate_certificate_html(candidate_data)

            return jsonify({
                'success': True,
                'html': html
            })
        except Exception as e:
            return jsonify({'success': False, 'message': str(e)})

    @app.route('/api/send_email/<int:index>', methods=['POST'])
    def api_send_email(index):
        try:
            if not email_service:
                return jsonify({'success': False, 'message': 'Email service not configured'})

            candidate = excel_manager.get_candidate_by_index(index)
            if not candidate:
                return jsonify({'success': False, 'message': 'Candidate not found'})

            if not candidate.get('Email'):
                return jsonify({'success': False, 'message': 'No email address for this candidate'})

            # Generate certificate image
            html = certificate_generator.generate_certificate_html(candidate)

            # Generate certificate image for attachment
            cert_image = None
            try:
                cert_image = certificate_generator.generate_certificate_image(candidate)
            except Exception as e:
                print(f"Warning: Could not generate certificate image: {e}")

            # Send email
            success, message = email_service.send_certificate_email(
                candidate['Email'],
                candidate['Name'],
                candidate.get('CertificateCode', ''),
                candidate['Project'],
                cert_image
            )

            return jsonify({'success': success, 'message': message})
        except Exception as e:
            return jsonify({'success': False, 'message': str(e)})

    @app.route('/api/delete_candidate/<int:index>', methods=['DELETE'])
    def api_delete_candidate(index):
        try:
            if excel_manager.delete_candidate(index):
                return jsonify({'success': True, 'message': 'Candidate deleted successfully'})
            else:
                return jsonify({'success': False, 'message': 'Error deleting candidate'})
        except Exception as e:
            return jsonify({'success': False, 'message': str(e)})

    @app.route('/api/bulk_send_certificates', methods=['POST'])
    def api_bulk_send_certificates():
        try:
            if not email_service:
                return jsonify({'success': False, 'message': 'Email service not configured'})

            data = request.get_json()
            filter_type = data.get('filter', 'all_with_email')
            selected_indices = data.get('selected_indices', [])
            generate_codes = data.get('generate_codes', True)
            attach_certificates = data.get('attach_certificates', True)

            candidates = excel_manager.read_all_candidates()

            # Filter candidates based on selection
            if filter_type == 'all_with_email':
                target_candidates = [(i, c) for i, c in enumerate(candidates) if c.get('Email')]
            elif filter_type == 'missing_codes':
                target_candidates = [(i, c) for i, c in enumerate(candidates)
                                   if c.get('Email') and not c.get('CertificateCode')]
            elif filter_type == 'custom_selection':
                target_candidates = [(i, candidates[i]) for i in selected_indices
                                   if i < len(candidates) and candidates[i].get('Email')]
            else:
                return jsonify({'success': False, 'message': 'Invalid filter type'})

            # Generate codes if requested
            if generate_codes:
                excel_manager.regenerate_all_certificate_codes()
                candidates = excel_manager.read_all_candidates()  # Refresh data

            # Send emails
            sent_count = 0
            failed_count = 0

            for candidate_index, candidate in target_candidates:
                try:
                    cert_image = None
                    if attach_certificates:
                        try:
                            cert_image = certificate_generator.generate_certificate_image(candidate)
                        except Exception as e:
                            print(f"Warning: Could not generate certificate image for {candidate.get('Name', 'Unknown')}: {e}")

                    success, send_message = email_service.send_certificate_email(
                        candidate['Email'],
                        candidate['Name'],
                        candidate.get('CertificateCode', ''),
                        candidate['Project'],
                        cert_image
                    )

                    if success:
                        sent_count += 1
                        print(f"Successfully sent certificate to {candidate.get('Name', 'Unknown')}")
                    else:
                        failed_count += 1
                        print(f"Failed to send certificate to {candidate.get('Name', 'Unknown')}: {send_message}")

                except Exception as e:
                    failed_count += 1
                    print(f"Error sending to {candidate.get('Name', 'Unknown')}: {e}")

            return jsonify({
                'success': True,
                'sent_count': sent_count,
                'failed_count': failed_count,
                'message': f'Sent {sent_count} certificates, {failed_count} failed'
            })

        except Exception as e:
            return jsonify({'success': False, 'message': str(e)})

    @app.route('/api/test_email', methods=['POST'])
    def api_test_email():
        try:
            if not email_service:
                return jsonify({'success': False, 'message': 'Email service not configured'})

            success, message = email_service.test_email_connection()
            return jsonify({'success': success, 'message': message})
        except Exception as e:
            return jsonify({'success': False, 'message': str(e)})

    @app.route('/api/download_all_certificates')
    def api_download_all_certificates():
        try:
            candidates = excel_manager.read_all_candidates()
            certificates = certificate_generator.generate_all_certificates_html(candidates)

            return jsonify({
                'success': True,
                'certificates': certificates
            })
        except Exception as e:
            return jsonify({'success': False, 'message': str(e)})

    @app.route('/api/download_certificates_with_email')
    def api_download_certificates_with_email():
        try:
            candidates = excel_manager.read_all_candidates()
            candidates_with_email = [c for c in candidates if c.get('Email')]
            certificates = certificate_generator.generate_all_certificates_html(candidates_with_email)

            return jsonify({
                'success': True,
                'certificates': certificates
            })
        except Exception as e:
            return jsonify({'success': False, 'message': str(e)})

    return app

# Create app instance
app = create_app()

if __name__ == '__main__':
    app.run(debug=True)
