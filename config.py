"""
Configuration settings for the Certificate Automation System
"""

import os
from datetime import timedelta
from dotenv import load_dotenv

# Load environment variables from .env file
load_dotenv()

class Config:
    # Flask Configuration
    SECRET_KEY = os.environ.get('SECRET_KEY') or 'your-secret-key-change-this-in-production'
    DEBUG = os.environ.get('FLASK_DEBUG', 'True').lower() == 'true'
    
    # File Paths
    EXCEL_FILE_PATH = os.path.join('static', 'data.xlsx')
    UPLOAD_FOLDER = os.path.join('static', 'uploads')
    CERTIFICATE_IMAGES_FOLDER = os.path.join('static', 'certificates')
    
    # Email Configuration
    GMAIL_USER = os.environ.get('GMAIL_USER')
    GMAIL_PASSWORD = os.environ.get('GMAIL_PASSWORD')  # Use App Password, not regular password
    
    # Email Settings
    SMTP_SERVER = 'smtp.gmail.com'
    SMTP_PORT = 587
    
    # Application Settings
    MAX_CANDIDATES_PER_BATCH = 50  # Maximum candidates to process in one batch
    CERTIFICATE_IMAGE_QUALITY = 2  # Scale factor for certificate images
    
    # Session Configuration
    PERMANENT_SESSION_LIFETIME = timedelta(hours=2)
    
    # File Upload Settings
    MAX_CONTENT_LENGTH = 16 * 1024 * 1024  # 16MB max file size
    ALLOWED_EXTENSIONS = {'xlsx', 'xls'}
    
    # Certificate Template Settings
    CERTIFICATE_WIDTH = 794   # A4 width in pixels at 96 DPI
    CERTIFICATE_HEIGHT = 1123 # A4 height in pixels at 96 DPI
    
    # Company Information
    COMPANY_NAME = "Innodatatics - USA"
    COMPANY_DESCRIPTION = "Emerging Technologies Consulting Firm"
    CEO_NAME = "Shirish G Kumar"
    CEO_TITLE = "CEO & MD"
    
    # Certificate Code Settings
    CERTIFICATE_PREFIX = "IDA-IN"
    
    @staticmethod
    def init_app(app):
        """Initialize application with configuration"""
        # Create necessary directories
        os.makedirs(Config.UPLOAD_FOLDER, exist_ok=True)
        os.makedirs(Config.CERTIFICATE_IMAGES_FOLDER, exist_ok=True)
        os.makedirs(os.path.dirname(Config.EXCEL_FILE_PATH), exist_ok=True)

class DevelopmentConfig(Config):
    """Development configuration"""
    DEBUG = True
    
class ProductionConfig(Config):
    """Production configuration"""
    DEBUG = False
    SECRET_KEY = os.environ.get('SECRET_KEY') or 'production-secret-key-must-be-set'
    
    @classmethod
    def init_app(cls, app):
        Config.init_app(app)
        
        # Log to stderr in production
        import logging
        from logging import StreamHandler
        file_handler = StreamHandler()
        file_handler.setLevel(logging.INFO)
        app.logger.addHandler(file_handler)

class TestingConfig(Config):
    """Testing configuration"""
    TESTING = True
    EXCEL_FILE_PATH = os.path.join('tests', 'test_data.xlsx')

# Configuration dictionary
config = {
    'development': DevelopmentConfig,
    'production': ProductionConfig,
    'testing': TestingConfig,
    'default': DevelopmentConfig
}

# Email configuration helper
def get_email_config():
    """Get email configuration with validation"""
    gmail_user = os.environ.get('GMAIL_USER')
    gmail_password = os.environ.get('GMAIL_PASSWORD')
    
    if not gmail_user or not gmail_password:
        return {
            'configured': False,
            'message': 'Gmail credentials not configured. Please set GMAIL_USER and GMAIL_PASSWORD environment variables.'
        }
    
    return {
        'configured': True,
        'gmail_user': gmail_user,
        'gmail_password': gmail_password,
        'message': 'Email configuration loaded successfully.'
    }

# Application constants
APP_CONSTANTS = {
    'APP_NAME': 'Certificate Automation System',
    'APP_VERSION': '2.0.0',
    'SUPPORTED_DATE_FORMATS': [
        'Jan 10 th 2025',
        'Feb 01 st 2025',
        '18-APR-2025',
        '2025-01-10',
        '01/10/2025',
        '10/01/2025'
    ],
    'QUARTER_MONTHS': {
        1: [1, 2, 3],    # Q1: Jan-Mar
        2: [4, 5, 6],    # Q2: Apr-Jun
        3: [7, 8, 9],    # Q3: Jul-Sep
        4: [10, 11, 12]  # Q4: Oct-Dec
    }
}

# Validation rules
VALIDATION_RULES = {
    'name': {
        'required': True,
        'min_length': 2,
        'max_length': 100
    },
    'email': {
        'required': False,  # Email is optional but recommended
        'pattern': r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$'
    },
    'project': {
        'required': True,
        'min_length': 5,
        'max_length': 200
    },
    'dates': {
        'required': True,
        'supported_formats': APP_CONSTANTS['SUPPORTED_DATE_FORMATS']
    }
}

# Default candidate template
DEFAULT_CANDIDATE = {
    'Name': '',
    'Email': '',
    'Project': '',
    'StartDate': '',
    'EndDate': '',
    'IssueDate': '',
    'CertificateCode': ''
}

# Success and error messages
MESSAGES = {
    'success': {
        'candidate_added': 'Candidate added successfully!',
        'candidate_updated': 'Candidate updated successfully!',
        'candidate_deleted': 'Candidate deleted successfully!',
        'codes_generated': 'Certificate codes generated for all candidates!',
        'email_sent': 'Certificate email sent successfully!',
        'bulk_emails_sent': 'Bulk certificates sent successfully!'
    },
    'error': {
        'candidate_not_found': 'Candidate not found!',
        'invalid_data': 'Invalid candidate data provided!',
        'email_not_configured': 'Email service not configured!',
        'email_send_failed': 'Failed to send email!',
        'file_not_found': 'Excel file not found!',
        'invalid_file_format': 'Invalid file format. Please use .xlsx or .xls files.'
    },
    'warning': {
        'no_email': 'No email address provided for this candidate.',
        'email_optional': 'Email is optional but recommended for certificate delivery.'
    }
}
