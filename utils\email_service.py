"""
Email Service for sending certificates via Gmail SMTP
"""

import smtplib
import os
from email.mime.multipart import MIMEMultipart
from email.mime.text import MIMEText
from email.mime.base import MIMEBase
from email import encoders
import tempfile
import base64
from datetime import datetime

class EmailService:
    def __init__(self, gmail_user=None, gmail_password=None):
        self.gmail_user = gmail_user or os.getenv('GMAIL_USER')
        self.gmail_password = gmail_password or os.getenv('GMAIL_PASSWORD')
        self.smtp_server = 'smtp.gmail.com'
        self.smtp_port = 587
    
    def validate_credentials(self):
        """Validate Gmail credentials"""
        if not self.gmail_user or not self.gmail_password:
            return False, "Gmail credentials not provided. Please set GMAIL_USER and GMAIL_PASSWORD environment variables."
        
        try:
            server = smtplib.SMTP(self.smtp_server, self.smtp_port)
            server.starttls()
            server.login(self.gmail_user, self.gmail_password)
            server.quit()
            return True, "Gmail credentials validated successfully."
        except smtplib.SMTPAuthenticationError:
            return False, "Gmail authentication failed. Please check your credentials and enable 'App Passwords' in your Google account."
        except Exception as e:
            return False, f"Error connecting to Gmail: {str(e)}"
    
    def create_certificate_email(self, candidate_name, certificate_code, project_name):
        """Create email content for certificate"""
        subject = f"🎓 Internship Certificate - {candidate_name}"
        
        html_body = f"""
        <html>
        <body style="font-family: Arial, sans-serif; line-height: 1.6; color: #333;">
            <div style="max-width: 600px; margin: 0 auto; padding: 20px;">
                <div style="text-align: center; margin-bottom: 30px;">
                    <h1 style="color: #004a77; margin-bottom: 10px;">🎓 Congratulations!</h1>
                    <h2 style="color: #1f5d8b; margin-top: 0;">Internship Certificate</h2>
                </div>
                
                <div style="background: #f8f9fa; padding: 20px; border-radius: 8px; margin-bottom: 20px;">
                    <p style="font-size: 16px; margin-bottom: 15px;">Dear <strong>{candidate_name}</strong>,</p>
                    
                    <p>Congratulations on successfully completing your internship with <strong>Innodatatics - USA</strong>!</p>
                    
                    <p>We are pleased to present you with your official internship certificate for the project:</p>
                    <p style="text-align: center; font-size: 18px; color: #004a77; font-weight: bold; 
                       background: white; padding: 15px; border-radius: 5px; margin: 20px 0;">
                        "{project_name}"
                    </p>
                    
                    <div style="background: white; padding: 15px; border-radius: 5px; border-left: 4px solid #004a77;">
                        <p style="margin: 0;"><strong>Certificate Code:</strong> {certificate_code}</p>
                    </div>
                </div>
                
                <div style="background: #e8f4f8; padding: 15px; border-radius: 8px; margin-bottom: 20px;">
                    <h3 style="color: #004a77; margin-top: 0;">📋 What's Next?</h3>
                    <ul style="margin: 10px 0;">
                        <li>Your certificate is attached to this email</li>
                        <li>Keep this certificate for your professional records</li>
                        <li>You can add this internship to your LinkedIn profile</li>
                        <li>Feel free to use us as a reference for future opportunities</li>
                    </ul>
                </div>
                
                <div style="text-align: center; margin-top: 30px; padding-top: 20px; border-top: 1px solid #ddd;">
                    <p style="color: #666; font-size: 14px;">
                        Thank you for your dedication and hard work during the internship.<br>
                        We wish you all the best in your future endeavors!
                    </p>
                    
                    <div style="margin-top: 20px;">
                        <p style="margin: 5px 0; font-weight: bold;">Innodatatics - USA</p>
                        <p style="margin: 5px 0; color: #666; font-size: 14px;">Emerging Technologies Consulting Firm</p>
                    </div>
                </div>
                
                <div style="text-align: center; margin-top: 20px; padding: 15px; background: #f1f1f1; border-radius: 5px;">
                    <p style="font-size: 12px; color: #888; margin: 0;">
                        This is an automated email. Please do not reply to this message.<br>
                        Certificate generated on {datetime.now().strftime('%B %d, %Y')}
                    </p>
                </div>
            </div>
        </body>
        </html>
        """
        
        text_body = f"""
        Congratulations {candidate_name}!
        
        You have successfully completed your internship with Innodatatics - USA.
        
        Project: {project_name}
        Certificate Code: {certificate_code}
        
        Your certificate is attached to this email.
        
        Thank you for your dedication and hard work during the internship.
        We wish you all the best in your future endeavors!
        
        Best regards,
        Innodatatics - USA
        Emerging Technologies Consulting Firm
        
        ---
        This is an automated email. Please do not reply to this message.
        Certificate generated on {datetime.now().strftime('%B %d, %Y')}
        """
        
        return subject, html_body, text_body
    
    def send_certificate_email(self, recipient_email, candidate_name, certificate_code,
                             project_name, certificate_data=None):
        """Send certificate email to recipient"""
        try:
            # Create email message
            msg = MIMEMultipart('alternative')
            subject, html_body, text_body = self.create_certificate_email(
                candidate_name, certificate_code, project_name
            )

            msg['From'] = self.gmail_user
            msg['To'] = recipient_email
            msg['Subject'] = subject

            # Add text and HTML parts
            msg.attach(MIMEText(text_body, 'plain'))
            msg.attach(MIMEText(html_body, 'html'))

            # Attach certificate if provided
            if certificate_data:
                try:
                    # Determine file type and create appropriate attachment
                    if isinstance(certificate_data, bytes):
                        # Assume it's PDF data
                        part = MIMEBase('application', 'pdf')
                        part.set_payload(certificate_data)
                        encoders.encode_base64(part)
                        filename = f"{candidate_name.replace(' ', '_')}_Certificate.pdf"
                        part.add_header(
                            'Content-Disposition',
                            f'attachment; filename="{filename}"'
                        )
                        msg.attach(part)

                    elif isinstance(certificate_data, str):
                        # Handle base64 encoded data
                        if certificate_data.startswith('data:'):
                            # Remove data URL prefix
                            header, data = certificate_data.split(',', 1)
                            decoded_data = base64.b64decode(data)

                            # Determine file type from header
                            if 'pdf' in header:
                                content_type = 'application/pdf'
                                extension = 'pdf'
                            else:
                                content_type = 'image/png'
                                extension = 'png'
                        else:
                            # Assume base64 encoded image
                            decoded_data = base64.b64decode(certificate_data)
                            content_type = 'image/png'
                            extension = 'png'

                        # Create temporary file
                        with tempfile.NamedTemporaryFile(suffix=f'.{extension}', delete=False) as tmp_file:
                            tmp_file.write(decoded_data)
                            tmp_file_path = tmp_file.name

                        # Attach the file
                        with open(tmp_file_path, 'rb') as attachment:
                            part = MIMEBase('application', 'octet-stream')
                            part.set_payload(attachment.read())

                        encoders.encode_base64(part)
                        filename = f"{candidate_name.replace(' ', '_')}_Certificate.{extension}"
                        part.add_header(
                            'Content-Disposition',
                            f'attachment; filename="{filename}"'
                        )
                        msg.attach(part)

                        # Clean up temporary file
                        os.unlink(tmp_file_path)

                except Exception as attach_error:
                    print(f"Warning: Could not attach certificate: {attach_error}")
                    # Continue sending email without attachment
            
            # Send email
            server = smtplib.SMTP(self.smtp_server, self.smtp_port)
            server.starttls()
            server.login(self.gmail_user, self.gmail_password)
            server.send_message(msg)
            server.quit()
            
            return True, f"Certificate sent successfully to {recipient_email}"
            
        except Exception as e:
            return False, f"Failed to send email to {recipient_email}: {str(e)}"
    
    def send_bulk_certificates(self, candidates_data, certificate_data_list=None):
        """Send certificates to multiple candidates"""
        results = []
        certificate_data_list = certificate_data_list or {}

        for i, candidate in enumerate(candidates_data):
            try:
                name = candidate.get('Name', '')
                email = candidate.get('Email', '')
                project = candidate.get('Project', '')
                code = candidate.get('CertificateCode', '')

                if not email or not name:
                    results.append({
                        'candidate': name,
                        'email': email,
                        'success': False,
                        'message': 'Missing name or email'
                    })
                    continue

                # Get certificate data for this candidate
                cert_data = certificate_data_list.get(i) or certificate_data_list.get(name)

                success, message = self.send_certificate_email(
                    email, name, code, project, cert_data
                )
                
                results.append({
                    'candidate': name,
                    'email': email,
                    'success': success,
                    'message': message
                })
                
            except Exception as e:
                results.append({
                    'candidate': candidate.get('Name', 'Unknown'),
                    'email': candidate.get('Email', 'Unknown'),
                    'success': False,
                    'message': f"Error: {str(e)}"
                })
        
        return results
    
    def test_email_connection(self):
        """Test email connection and credentials"""
        return self.validate_credentials()

# Example usage
if __name__ == "__main__":
    # Test email service
    email_service = EmailService()
    
    # Test credentials
    valid, message = email_service.test_email_connection()
    print(f"Email test: {message}")
    
    if valid:
        # Test sending email (uncomment to test)
        # success, msg = email_service.send_certificate_email(
        #     "<EMAIL>",
        #     "Test User",
        #     "IDA-IN/25/Q1/01/0001",
        #     "Test Project"
        # )
        # print(f"Send test: {msg}")
        pass
