#!/usr/bin/env python3
"""
Comprehensive Test Script for Certificate Automation System
Tests all major functionality including certificate generation, email sending, and UI components
"""

import os
import sys
import json
import tempfile
import traceback
from datetime import datetime

# Add the project root to Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from utils.excel_manager import ExcelManager
from utils.certificate_generator import CertificateGenerator
from utils.email_service import EmailService
from utils.code_generator import CertificateCodeGenerator
from config import get_email_config

class SystemTester:
    def __init__(self):
        self.results = {
            'passed': 0,
            'failed': 0,
            'tests': []
        }

        # Initialize components
        self.excel_manager = ExcelManager('static/data.xlsx')
        self.certificate_generator = CertificateGenerator()
        self.code_generator = CertificateCodeGenerator()

        # Email service (optional)
        email_config = get_email_config()
        self.email_service = EmailService() if email_config['configured'] else None

        print("🚀 Certificate Automation System Test Suite")
        print("=" * 50)

    def log_test(self, test_name, passed, message=""):
        """Log test result"""
        status = "✅ PASS" if passed else "❌ FAIL"
        print(f"{status} {test_name}")
        if message:
            print(f"    {message}")

        self.results['tests'].append({
            'name': test_name,
            'passed': passed,
            'message': message
        })

        if passed:
            self.results['passed'] += 1
        else:
            self.results['failed'] += 1

def print_test_header(test_name):
    print(f"\n{'='*50}")
    print(f"🧪 Testing: {test_name}")
    print(f"{'='*50}")

def print_result(test_name, success, message=""):
    status = "✅ PASS" if success else "❌ FAIL"
    print(f"{status} {test_name}")
    if message:
        print(f"   {message}")

def test_imports():
    """Test that all required modules can be imported"""
    print_test_header("Module Imports")
    
    tests = [
        ("Flask", lambda: __import__('flask')),
        ("Pandas", lambda: __import__('pandas')),
        ("OpenPyXL", lambda: __import__('openpyxl')),
        ("Config", lambda: __import__('config')),
        ("Excel Manager", lambda: __import__('utils.excel_manager')),
        ("Certificate Generator", lambda: __import__('utils.certificate_generator')),
        ("Email Service", lambda: __import__('utils.email_service')),
        ("Code Generator", lambda: __import__('utils.code_generator')),
    ]
    
    all_passed = True
    for test_name, import_func in tests:
        try:
            import_func()
            print_result(test_name, True)
        except ImportError as e:
            print_result(test_name, False, str(e))
            all_passed = False
        except Exception as e:
            print_result(test_name, False, f"Unexpected error: {e}")
            all_passed = False
    
    return all_passed

def test_excel_operations():
    """Test Excel file operations"""
    print_test_header("Excel Operations")
    
    try:
        from utils.excel_manager import ExcelManager
        
        # Test Excel manager initialization
        excel_manager = ExcelManager('static/data.xlsx')
        print_result("Excel Manager Init", True)
        
        # Test reading candidates
        candidates = excel_manager.read_all_candidates()
        print_result("Read Candidates", True, f"Found {len(candidates)} candidates")
        
        # Test candidate validation
        test_candidate = {
            'Name': 'Test User',
            'Email': '<EMAIL>',
            'Project': 'Test Project',
            'StartDate': 'Jan 15 th 2025',
            'EndDate': 'Apr 15 th 2025',
            'IssueDate': '20-APR-2025'
        }
        
        errors = excel_manager.validate_candidate_data(test_candidate)
        print_result("Data Validation", len(errors) == 0, f"Validation errors: {errors}")
        
        return True
        
    except Exception as e:
        print_result("Excel Operations", False, str(e))
        return False

def test_certificate_generation():
    """Test certificate code generation"""
    print_test_header("Certificate Generation")
    
    try:
        from utils.code_generator import CertificateCodeGenerator
        from utils.certificate_generator import CertificateGenerator
        
        # Test code generator
        code_gen = CertificateCodeGenerator()
        
        # Test date parsing
        test_dates = [
            "Jan 15 th 2025",
            "Feb 01 st 2025",
            "18-APR-2025",
            "2025-03-15"
        ]
        
        for date_str in test_dates:
            try:
                parsed = code_gen.parse_date_string(date_str)
                code = code_gen.generate_certificate_code(date_str)
                print_result(f"Date Parse: {date_str}", True, f"Code: {code}")
            except Exception as e:
                print_result(f"Date Parse: {date_str}", False, str(e))
        
        # Test certificate HTML generation
        cert_gen = CertificateGenerator()
        test_candidate = {
            'Name': 'Test User',
            'Project': 'Test Project',
            'StartDate': 'Jan 15 th 2025',
            'EndDate': 'Apr 15 th 2025',
            'IssueDate': '20-APR-2025',
            'CertificateCode': 'IDA-IN/25/Q1/01/0001'
        }
        
        html = cert_gen.generate_certificate_html(test_candidate)
        print_result("Certificate HTML", len(html) > 1000, f"HTML length: {len(html)} chars")
        
        return True
        
    except Exception as e:
        print_result("Certificate Generation", False, str(e))
        traceback.print_exc()
        return False

def test_email_service():
    """Test email service configuration"""
    print_test_header("Email Service")
    
    try:
        from utils.email_service import EmailService
        from config import get_email_config
        
        # Test email configuration
        email_config = get_email_config()
        print_result("Email Config", email_config['configured'], email_config['message'])
        
        if email_config['configured']:
            # Test email service initialization
            email_service = EmailService()
            print_result("Email Service Init", True)
            
            # Test email template creation
            subject, html_body, text_body = email_service.create_certificate_email(
                "Test User", "IDA-IN/25/Q1/01/0001", "Test Project"
            )
            print_result("Email Template", len(html_body) > 500, f"Template length: {len(html_body)} chars")
        
        return True
        
    except Exception as e:
        print_result("Email Service", False, str(e))
        return False

def test_flask_app():
    """Test Flask application setup"""
    print_test_header("Flask Application")
    
    try:
        from app_3 import create_app
        
        # Test app creation
        app = create_app('testing')
        print_result("App Creation", True)
        
        # Test app configuration
        print_result("App Config", app.config['TESTING'], "Testing mode enabled")
        
        # Test routes exist
        with app.app_context():
            routes = [rule.rule for rule in app.url_map.iter_rules()]
            expected_routes = ['/', '/add_candidate', '/manage_candidates', '/bulk_operations']
            
            for route in expected_routes:
                exists = route in routes
                print_result(f"Route: {route}", exists)
        
        return True
        
    except Exception as e:
        print_result("Flask Application", False, str(e))
        traceback.print_exc()
        return False

def test_file_structure():
    """Test that required files and directories exist"""
    print_test_header("File Structure")
    
    required_files = [
        'app_3.py',
        'config.py',
        'requirements.txt',
        'utils/__init__.py',
        'utils/excel_manager.py',
        'utils/certificate_generator.py',
        'utils/email_service.py',
        'utils/code_generator.py',
        'templates/base.html',
        'templates/index.html',
        'templates/add_candidate.html',
        'templates/manage_candidates.html',
        'templates/bulk_operations.html',
        'templates/certificate.html',
        'static/css/style.css'
    ]
    
    required_dirs = [
        'utils',
        'templates',
        'static',
        'static/css'
    ]
    
    all_passed = True
    
    for file_path in required_files:
        exists = os.path.exists(file_path)
        print_result(f"File: {file_path}", exists)
        if not exists:
            all_passed = False
    
    for dir_path in required_dirs:
        exists = os.path.isdir(dir_path)
        print_result(f"Directory: {dir_path}", exists)
        if not exists:
            all_passed = False
    
    return all_passed

def run_all_tests():
    """Run all tests and provide summary"""
    print("🎓 Certificate Automation System - Test Suite")
    print(f"Started at: {datetime.now().strftime('%Y-%m-%d %H:%M:%S')}")
    
    tests = [
        ("File Structure", test_file_structure),
        ("Module Imports", test_imports),
        ("Excel Operations", test_excel_operations),
        ("Certificate Generation", test_certificate_generation),
        ("Email Service", test_email_service),
        ("Flask Application", test_flask_app),
    ]
    
    results = []
    for test_name, test_func in tests:
        try:
            result = test_func()
            results.append((test_name, result))
        except Exception as e:
            print(f"\n❌ {test_name} failed with exception: {e}")
            results.append((test_name, False))
    
    # Print summary
    print(f"\n{'='*60}")
    print("📊 TEST SUMMARY")
    print(f"{'='*60}")
    
    passed = sum(1 for _, result in results if result)
    total = len(results)
    
    for test_name, result in results:
        status = "✅ PASS" if result else "❌ FAIL"
        print(f"{status} {test_name}")
    
    print(f"\nResults: {passed}/{total} tests passed")
    
    if passed == total:
        print("🎉 All tests passed! System is ready to use.")
        return True
    else:
        print("⚠️  Some tests failed. Please check the errors above.")
        return False

if __name__ == "__main__":
    try:
        success = run_all_tests()
        sys.exit(0 if success else 1)
    except KeyboardInterrupt:
        print("\n\n❌ Tests cancelled by user")
        sys.exit(1)
    except Exception as e:
        print(f"\n❌ Test suite failed: {e}")
        traceback.print_exc()
        sys.exit(1)
