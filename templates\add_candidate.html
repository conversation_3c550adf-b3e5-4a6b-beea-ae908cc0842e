{% extends "base.html" %}

{% block title %}Add Candidate - Certificate Automation{% endblock %}

{% block extra_head %}
<!-- Flatpickr CSS -->
<link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/flatpickr/dist/flatpickr.min.css">
<link rel="stylesheet" href="https://cdn.jsdelivr.net/npm/flatpickr/dist/themes/material_blue.css">

<style>
    .date-input-group {
        position: relative;
    }

    .date-input-group .form-control {
        padding-right: 45px;
    }

    .date-input-group .date-icon {
        position: absolute;
        right: 12px;
        top: 50%;
        transform: translateY(-50%);
        color: var(--primary-color);
        pointer-events: none;
        z-index: 10;
    }

    .flatpickr-calendar {
        border-radius: 12px;
        box-shadow: var(--shadow-xl);
        border: 1px solid var(--border-color);
    }

    .flatpickr-day.selected {
        background: var(--primary-color);
        border-color: var(--primary-color);
    }

    .flatpickr-day:hover {
        background: var(--primary-light);
        border-color: var(--primary-light);
    }
</style>
{% endblock %}

{% block content %}
<div class="row justify-content-center">
    <div class="col-lg-8">
        <div class="card">
            <div class="card-header">
                <h5 class="card-title">
                    <i class="bi bi-person-plus-fill"></i>
                    {% if edit_mode %}Edit Candidate{% else %}Add New Candidate{% endif %}
                </h5>
            </div>
            <div class="card-body">
                <form method="POST" id="candidateForm">
                    <div class="row">
                        <!-- Name Field -->
                        <div class="col-md-6 mb-3">
                            <label for="name" class="form-label">
                                <i class="bi bi-person-fill text-primary me-1"></i>
                                Full Name <span class="text-danger">*</span>
                            </label>
                            <input type="text" class="form-control" id="name" name="name" 
                                   value="{{ candidate.Name if candidate else '' }}" 
                                   required maxlength="100" placeholder="Enter full name">
                            <div class="form-text">Enter the candidate's complete name as it should appear on the certificate</div>
                        </div>

                        <!-- Email Field -->
                        <div class="col-md-6 mb-3">
                            <label for="email" class="form-label">
                                <i class="bi bi-envelope-fill text-info me-1"></i>
                                Email Address
                            </label>
                            <input type="email" class="form-control" id="email" name="email" 
                                   value="{{ candidate.Email if candidate else '' }}" 
                                   placeholder="Enter email address">
                            <div class="form-text">Email is optional but recommended for certificate delivery</div>
                        </div>
                    </div>

                    <!-- Project Field -->
                    <div class="mb-3">
                        <label for="project" class="form-label">
                            <i class="bi bi-briefcase-fill text-success me-1"></i>
                            Project Title <span class="text-danger">*</span>
                        </label>
                        <input type="text" class="form-control" id="project" name="project" 
                               value="{{ candidate.Project if candidate else '' }}" 
                               required maxlength="200" placeholder="Enter project title">
                        <div class="form-text">The internship project title that will appear on the certificate</div>
                    </div>

                    <div class="row">
                        <!-- Start Date Field -->
                        <div class="col-md-4 mb-3">
                            <label for="start_date" class="form-label">
                                <i class="bi bi-calendar-event-fill text-warning me-1"></i>
                                Start Date <span class="text-danger">*</span>
                            </label>
                            <div class="date-input-group">
                                <input type="text" class="form-control date-picker" id="start_date" name="start_date"
                                       value="{{ candidate.StartDate if candidate else '' }}"
                                       required placeholder="Select start date" readonly>
                                <i class="fas fa-calendar-alt date-icon"></i>
                            </div>
                            <div class="form-text">Internship start date</div>
                        </div>

                        <!-- End Date Field -->
                        <div class="col-md-4 mb-3">
                            <label for="end_date" class="form-label">
                                <i class="bi bi-calendar-check-fill text-warning me-1"></i>
                                End Date <span class="text-danger">*</span>
                            </label>
                            <div class="date-input-group">
                                <input type="text" class="form-control date-picker" id="end_date" name="end_date"
                                       value="{{ candidate.EndDate if candidate else '' }}"
                                       required placeholder="Select end date" readonly>
                                <i class="fas fa-calendar-alt date-icon"></i>
                            </div>
                            <div class="form-text">Internship end date</div>
                        </div>

                        <!-- Issue Date Field -->
                        <div class="col-md-4 mb-3">
                            <label for="issue_date" class="form-label">
                                <i class="bi bi-calendar-plus-fill text-secondary me-1"></i>
                                Issue Date
                            </label>
                            <div class="date-input-group">
                                <input type="text" class="form-control date-picker" id="issue_date" name="issue_date"
                                       value="{{ candidate.IssueDate if candidate else '' }}"
                                       placeholder="Select issue date (optional)">
                                <i class="fas fa-calendar-alt date-icon"></i>
                            </div>
                            <div class="form-text">Certificate issue date (auto-generated if empty)</div>
                        </div>
                    </div>

                    <!-- Certificate Code Display -->
                    {% if candidate and candidate.CertificateCode %}
                    <div class="mb-3">
                        <label class="form-label">
                            <i class="bi bi-qr-code text-primary me-1"></i>
                            Certificate Code
                        </label>
                        <div class="input-group">
                            <input type="text" class="form-control" value="{{ candidate.CertificateCode }}" readonly>
                            <button class="btn btn-outline-secondary" type="button" onclick="regenerateCode()">
                                <i class="bi bi-arrow-clockwise"></i> Regenerate
                            </button>
                        </div>
                        <div class="form-text">Certificate code will be auto-generated based on start date</div>
                    </div>
                    {% endif %}

                    <!-- Date Format Help -->
                    <div class="alert alert-info">
                        <h6><i class="bi bi-info-circle-fill me-2"></i>Supported Date Formats:</h6>
                        <ul class="mb-0">
                            <li><code>Jan 15 th 2025</code> (recommended)</li>
                            <li><code>Feb 01 st 2025</code></li>
                            <li><code>18-APR-2025</code></li>
                            <li><code>2025-01-15</code></li>
                            <li><code>01/15/2025</code></li>
                        </ul>
                    </div>

                    <!-- Form Actions -->
                    <div class="d-flex justify-content-between align-items-center">
                        <a href="{{ url_for('index') }}" class="btn btn-outline-secondary">
                            <i class="bi bi-arrow-left me-2"></i>Back to Dashboard
                        </a>
                        
                        <div>
                            {% if edit_mode %}
                            <button type="button" class="btn btn-outline-danger me-2" onclick="deleteCandidate()">
                                <i class="bi bi-trash-fill me-2"></i>Delete
                            </button>
                            {% endif %}
                            
                            <button type="button" class="btn btn-outline-primary me-2" onclick="previewCertificate()">
                                <i class="bi bi-eye me-2"></i>Preview Certificate
                            </button>
                            
                            <button type="submit" class="btn btn-primary">
                                <i class="bi bi-check-circle-fill me-2"></i>
                                <span class="btn-text">{% if edit_mode %}Update Candidate{% else %}Add Candidate{% endif %}</span>
                                <span class="loading-spinner spinner-border spinner-border-sm" role="status"></span>
                            </button>
                        </div>
                    </div>
                </form>
            </div>
        </div>
    </div>
</div>

<!-- Certificate Preview Modal -->
<div class="modal fade" id="certificateModal" tabindex="-1">
    <div class="modal-dialog modal-xl">
        <div class="modal-content">
            <div class="modal-header">
                <h5 class="modal-title">Certificate Preview</h5>
                <button type="button" class="btn-close" data-bs-dismiss="modal"></button>
            </div>
            <div class="modal-body" id="certificatePreview">
                <!-- Certificate preview will be loaded here -->
            </div>
            <div class="modal-footer">
                <button type="button" class="btn btn-secondary" data-bs-dismiss="modal">Close</button>
                <button type="button" class="btn btn-primary" onclick="downloadPreviewCertificate()">
                    <i class="bi bi-download me-2"></i>Download Preview
                </button>
            </div>
        </div>
    </div>
</div>
{% endblock %}

{% block extra_js %}
<script>
    // Form submission with loading state
    document.getElementById('candidateForm').addEventListener('submit', function(e) {
        const submitBtn = this.querySelector('button[type="submit"]');
        showLoading(submitBtn);
    });

    // Preview certificate with current form data
    async function previewCertificate() {
        const formData = new FormData(document.getElementById('candidateForm'));
        const candidateData = Object.fromEntries(formData);
        
        try {
            const response = await fetch('/api/preview_certificate', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify(candidateData)
            });
            
            const result = await response.json();
            
            if (result.success) {
                document.getElementById('certificatePreview').innerHTML = result.html;
                const modal = new bootstrap.Modal(document.getElementById('certificateModal'));
                modal.show();
            } else {
                alert('Error: ' + result.message);
            }
        } catch (error) {
            alert('Error loading preview: ' + error.message);
        }
    }

    // Download preview certificate
    function downloadPreviewCertificate() {
        const certificate = document.querySelector('#certificateModal .certificate');
        
        if (certificate) {
            html2canvas(certificate, { 
                scale: 2,
                useCORS: true,
                allowTaint: true 
            }).then(canvas => {
                const link = document.createElement('a');
                const name = document.getElementById('name').value.replace(/\s+/g, '_') || 'Certificate';
                link.download = `${name}_Preview.png`;
                link.href = canvas.toDataURL('image/png');
                link.click();
            });
        }
    }

    // Regenerate certificate code
    async function regenerateCode() {
        const startDate = document.getElementById('start_date').value;
        
        if (!startDate) {
            alert('Please enter a start date first');
            return;
        }
        
        try {
            const response = await fetch('/api/generate_code', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({ start_date: startDate })
            });
            
            const result = await response.json();
            
            if (result.success) {
                location.reload(); // Refresh to show new code
            } else {
                alert('Error: ' + result.message);
            }
        } catch (error) {
            alert('Error generating code: ' + error.message);
        }
    }

    // Delete candidate (only in edit mode)
    async function deleteCandidate() {
        if (!confirm('Are you sure you want to delete this candidate? This action cannot be undone.')) {
            return;
        }
        
        const candidateIndex = {{ candidate_index if edit_mode else -1 }};
        
        try {
            const response = await fetch(`/api/delete_candidate/${candidateIndex}`, {
                method: 'DELETE'
            });
            
            const result = await response.json();
            
            if (result.success) {
                window.location.href = '{{ url_for("index") }}';
            } else {
                alert('Error: ' + result.message);
            }
        } catch (error) {
            alert('Error deleting candidate: ' + error.message);
        }
    }

    // Auto-format dates as user types
    document.getElementById('start_date').addEventListener('blur', formatDate);
    document.getElementById('end_date').addEventListener('blur', formatDate);
    document.getElementById('issue_date').addEventListener('blur', formatDate);

    function formatDate(event) {
        const input = event.target;
        const value = input.value.trim();
        
        if (!value) return;
        
        // Try to parse and reformat common date patterns
        const datePatterns = [
            { regex: /^(\d{1,2})\/(\d{1,2})\/(\d{4})$/, format: (m) => `${getMonthName(parseInt(m[1]))} ${m[2]} ${getOrdinal(parseInt(m[2]))} ${m[3]}` },
            { regex: /^(\d{4})-(\d{1,2})-(\d{1,2})$/, format: (m) => `${getMonthName(parseInt(m[2]))} ${m[3]} ${getOrdinal(parseInt(m[3]))} ${m[1]}` }
        ];
        
        for (const pattern of datePatterns) {
            const match = value.match(pattern.regex);
            if (match) {
                input.value = pattern.format(match);
                break;
            }
        }
    }

    function getMonthName(month) {
        const months = ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun', 'Jul', 'Aug', 'Sep', 'Oct', 'Nov', 'Dec'];
        return months[month - 1] || 'Jan';
    }

    function getOrdinal(day) {
        if (day > 3 && day < 21) return 'th';
        switch (day % 10) {
            case 1: return 'st';
            case 2: return 'nd';
            case 3: return 'rd';
            default: return 'th';
        }
    }
</script>
{% endblock %}
