"""
Certificate Generator
Handles certificate generation and image conversion
"""

import base64
import tempfile
import os
from datetime import datetime

class CertificateGenerator:
    def __init__(self):
        self.certificate_template = None
    
    def format_date_for_display(self, date_str):
        """Format date string for display on certificate"""
        if not date_str:
            return ""
        
        try:
            # Try to parse the date and format it nicely
            from .code_generator import CertificateCodeGenerator
            generator = CertificateCodeGenerator()
            parsed_date = generator.parse_date_string(str(date_str))
            return parsed_date.strftime("%B %d, %Y")
        except:
            # If parsing fails, return original string
            return str(date_str)
    
    def generate_certificate_html(self, candidate_data):
        """Generate HTML for a single certificate"""
        
        # Format dates for display
        start_date = self.format_date_for_display(candidate_data.get('StartDate', ''))
        end_date = self.format_date_for_display(candidate_data.get('EndDate', ''))
        issue_date = self.format_date_for_display(candidate_data.get('IssueDate', ''))
        
        # If issue date is empty, use current date
        if not issue_date:
            issue_date = datetime.now().strftime("%B %d, %Y")
        
        certificate_html = f"""
        <!DOCTYPE html>
        <html lang="en">
        <head>
            <meta charset="UTF-8">
            <meta name="viewport" content="width=device-width, initial-scale=1.0">
            <title>Certificate - {candidate_data.get('Name', '')}</title>
            <link href="https://fonts.googleapis.com/css2?family=Playfair+Display:wght@400;600;700;900&family=Inter:wght@300;400;500;600;700&family=Crimson+Text:ital,wght@0,400;0,600;1,400&family=Cormorant+Garamond:ital,wght@0,300;0,400;0,500;0,600;0,700;1,400;1,500&display=swap" rel="stylesheet">
            <style>
                body {{
                    margin: 0;
                    padding: 20px;
                    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
                    font-family: 'Cormorant Garamond', serif;
                    display: flex;
                    justify-content: center;
                    align-items: center;
                    min-height: 100vh;
                }}

                .certificate {{
                    width: 794px;
                    height: 1123px;
                    background: linear-gradient(145deg, #ffffff 0%, #fefefe 100%);
                    margin: 0 auto;
                    padding: 0;
                    position: relative;
                    box-shadow:
                        0 30px 60px rgba(0,0,0,0.2),
                        0 20px 40px rgba(0,0,0,0.15),
                        inset 0 2px 0 rgba(255,255,255,0.8),
                        inset 0 -1px 0 rgba(0,0,0,0.05);
                    box-sizing: border-box;
                    border-radius: 12px;
                    overflow: hidden;
                    border: 1px solid rgba(0,74,119,0.1);
                }}

                /* Elegant border design with ornate patterns */
                .certificate::before {{
                    content: '';
                    position: absolute;
                    top: 25px;
                    left: 25px;
                    right: 25px;
                    bottom: 25px;
                    border: 4px solid transparent;
                    background: linear-gradient(45deg, #004a77, #1f5d8b, #2980b9, #004a77) border-box;
                    border-radius: 8px;
                    z-index: 1;
                    box-shadow: inset 0 0 20px rgba(0,74,119,0.1);
                }}

                .certificate::after {{
                    content: '';
                    position: absolute;
                    top: 35px;
                    left: 35px;
                    right: 35px;
                    bottom: 35px;
                    border: 2px solid rgba(0,74,119,0.15);
                    border-radius: 6px;
                    z-index: 1;
                    background:
                        radial-gradient(circle at 20% 20%, rgba(0,74,119,0.03) 0%, transparent 50%),
                        radial-gradient(circle at 80% 80%, rgba(31,93,139,0.03) 0%, transparent 50%);
                }}

                .certificate-content {{
                    position: relative;
                    z-index: 2;
                    padding: 60px 55px;
                    height: 100%;
                    display: flex;
                    flex-direction: column;
                    background:
                        radial-gradient(ellipse at top, rgba(255,255,255,0.8) 0%, transparent 70%),
                        radial-gradient(ellipse at bottom, rgba(248,249,250,0.6) 0%, transparent 70%);
                }}

                .header-container {{
                    text-align: center;
                    margin-bottom: 40px;
                    position: relative;
                }}

                .logo-img {{
                    width: 360px;
                    height: auto;
                    max-width: 100%;
                    filter: drop-shadow(0 4px 8px rgba(0,0,0,0.15));
                }}

                .title {{
                    text-align: center;
                    font-size: 38px;
                    font-weight: 700;
                    font-style: italic;
                    margin: 50px 0 25px;
                    color: #1a365d;
                    letter-spacing: 4px;
                    text-transform: uppercase;
                    text-shadow: 2px 2px 4px rgba(0,0,0,0.1);
                    position: relative;
                    font-family: 'Playfair Display', serif;
                }}

                .title::after {{
                    content: '';
                    position: absolute;
                    bottom: -15px;
                    left: 50%;
                    transform: translateX(-50%);
                    width: 250px;
                    height: 4px;
                    background: linear-gradient(90deg, transparent, #004a77, #1f5d8b, #004a77, transparent);
                    border-radius: 2px;
                    box-shadow: 0 2px 4px rgba(0,74,119,0.3);
                }}

                .subtitle {{
                    text-align: center;
                    font-size: 22px;
                    margin-bottom: 35px;
                    color: #2d3748;
                    font-style: italic;
                    font-weight: 400;
                    font-family: 'Cormorant Garamond', serif;
                    letter-spacing: 1px;
                }}

                .name {{
                    text-align: center;
                    font-size: 48px;
                    font-weight: 700;
                    color: #1f5d8b;
                    margin: 40px 0;
                    text-transform: uppercase;
                    letter-spacing: 5px;
                    text-shadow: 3px 3px 6px rgba(0,0,0,0.15);
                    position: relative;
                    padding: 20px 0;
                    font-family: 'Playfair Display', serif;
                }}

                .name::before,
                .name::after {{
                    content: '';
                    position: absolute;
                    left: 50%;
                    transform: translateX(-50%);
                    width: 350px;
                    height: 3px;
                    background: linear-gradient(90deg, transparent, #1f5d8b, #2980b9, #1f5d8b, transparent);
                    border-radius: 1.5px;
                }}

                .name::before {{
                    top: 0;
                    box-shadow: 0 2px 4px rgba(31,93,139,0.3);
                }}

                .name::after {{
                    bottom: 0;
                    box-shadow: 0 -2px 4px rgba(31,93,139,0.3);
                }}

                .project {{
                    text-align: center;
                    font-size: 20px;
                    margin-bottom: 20px;
                    line-height: 1.9;
                    color: #2d3748;
                    padding: 0 50px;
                    font-family: 'Cormorant Garamond', serif;
                }}

                .project-name {{
                    font-weight: 600;
                    color: #004a77;
                    font-size: 24px;
                    display: inline-block;
                    padding: 12px 25px;
                    background: linear-gradient(135deg, rgba(0,74,119,0.08) 0%, rgba(31,93,139,0.05) 100%);
                    border-radius: 12px;
                    margin: 15px 0;
                    border: 2px solid rgba(0,74,119,0.15);
                    box-shadow: 0 4px 8px rgba(0,74,119,0.1);
                    font-family: 'Playfair Display', serif;
                    font-style: italic;
                }}

                .firm {{
                    text-align: center;
                    font-size: 19px;
                    margin-bottom: 50px;
                    line-height: 2.1;
                    color: #2d3748;
                    padding: 0 40px;
                    font-family: 'Cormorant Garamond', serif;
                }}

                .firm-name {{
                    font-weight: 600;
                    color: #004a77;
                    font-size: 20px;
                    font-family: 'Playfair Display', serif;
                    font-style: italic;
                }}

                .date-range {{
                    font-weight: 700;
                    color: #1f5d8b;
                    font-family: 'Inter', sans-serif;
                }}

                .seal {{
                    text-align: center;
                    margin-top: 40px;
                    position: relative;
                }}

                .seal-img {{
                    width: 180px;
                    height: auto;
                    filter: drop-shadow(0 4px 8px rgba(0,0,0,0.2));
                }}

                .code {{
                    text-align: center;
                    font-size: 19px;
                    margin-top: 30px;
                    color: #1f5d8b;
                    font-weight: 700;
                    background: linear-gradient(135deg, #f8f9fa, #e9ecef, #f1f3f4);
                    padding: 15px 25px;
                    border-radius: 12px;
                    border: 3px solid #dee2e6;
                    display: inline-block;
                    box-shadow:
                        0 4px 8px rgba(0,0,0,0.1),
                        inset 0 1px 0 rgba(255,255,255,0.8);
                    font-family: 'Courier New', monospace;
                    letter-spacing: 1.5px;
                    position: relative;
                }}

                .footer {{
                    position: absolute;
                    bottom: 70px;
                    left: 60px;
                    right: 60px;
                    display: flex;
                    justify-content: space-between;
                    align-items: flex-end;
                    font-size: 15px;
                    font-family: 'Inter', sans-serif;
                }}

                .footer .date {{
                    text-align: center;
                    width: 220px;
                }}

                .footer .issued-date {{
                    display: inline-block;
                    border-top: 4px solid #2d3748;
                    padding-top: 12px;
                    margin-bottom: 10px;
                    font-weight: 700;
                    min-width: 160px;
                    font-size: 16px;
                    color: #2d3748;
                }}

                .footer .date-label {{
                    font-size: 17px;
                    color: #4a5568;
                    font-weight: 600;
                    margin-bottom: 18px;
                    text-transform: uppercase;
                    letter-spacing: 1px;
                }}

                .footer .sign {{
                    text-align: center;
                    width: 240px;
                }}

                .sign-img {{
                    width: 200px;
                    margin-bottom: 10px;
                    filter: drop-shadow(0 2px 4px rgba(0,0,0,0.15));
                }}

                .sign-name {{
                    display: inline-block;
                    border-top: 4px solid #2d3748;
                    padding-top: 10px;
                    font-weight: 700;
                    min-width: 180px;
                    font-size: 16px;
                    color: #2d3748;
                }}

                .sign-title {{
                    font-size: 14px;
                    color: #4a5568;
                    margin-top: 6px;
                    font-weight: 600;
                    text-transform: uppercase;
                    letter-spacing: 0.5px;
                }}

                /* Print styles */
                @media print {{
                    body {{
                        background: white;
                        padding: 0;
                    }}
                    .certificate {{
                        box-shadow: none;
                        margin: 0;
                    }}
                }}
            </style>
        </head>
        <body>
            <div class="certificate" id="certificate">
                <div class="certificate-content">
                    <div class="header-container">
                        <img src="/static/logo.png" alt="Innodatatics Logo" class="logo-img">
                    </div>

                    <div class="title">Certificate of Completion</div>
                    <div class="subtitle">This certificate is presented to</div>

                    <div class="name">{candidate_data.get('Name', '')}</div>

                    <div class="project">
                        for successfully completing the Internship Project on
                        <br><br>
                        <span class="project-name">"{candidate_data.get('Project', '')}"</span>
                    </div>

                    <div class="firm">
                        through emerging technologies consulting firm
                        <br><br>
                        <span class="firm-name">"Innodatatics - USA"</span>
                        <br>
                        from <span class="date-range">{start_date}</span> to <span class="date-range">{end_date}</span>
                    </div>

                    <div class="seal">
                        <img src="/static/badges.png" alt="Seal Badge" class="seal-img">
                        <br>
                        <div class="code">Certificate Code: {candidate_data.get('CertificateCode', '')}</div>
                    </div>
                </div>

                <div class="footer">
                    <div class="date">
                        <div class="date-label">Date</div>
                        <div class="issued-date">{issue_date}</div>
                    </div>
                    <div class="sign">
                        <img src="/static/signature.png" alt="Signature" class="sign-img">
                        <div class="sign-name">Shirish G Kumar</div>
                        <div class="sign-title">CEO & MD</div>
                    </div>
                </div>
            </div>
        </body>
        </html>
        """
        
        return certificate_html
    
    def generate_certificate_image(self, candidate_data):
        """Generate certificate image for email attachment"""
        try:
            # For now, we'll return None as image generation requires additional dependencies
            # In a full implementation, you would use libraries like wkhtmltopdf, playwright, or selenium
            # to convert HTML to image

            # Placeholder implementation - return None to indicate no image attachment
            # The email will still be sent without the image attachment
            return None

        except Exception as e:
            print(f"Error generating certificate image: {e}")
            return None

    def generate_all_certificates_html(self, candidates_data):
        """Generate HTML for all certificates"""
        all_certificates = []

        for candidate in candidates_data:
            cert_html = self.generate_certificate_html(candidate)
            all_certificates.append({
                'candidate': candidate,
                'html': cert_html
            })

        return all_certificates
    
    def create_certificate_preview_data(self, candidate_data):
        """Create data structure for certificate preview"""
        return {
            'name': candidate_data.get('Name', ''),
            'project': candidate_data.get('Project', ''),
            'start_date': self.format_date_for_display(candidate_data.get('StartDate', '')),
            'end_date': self.format_date_for_display(candidate_data.get('EndDate', '')),
            'issue_date': self.format_date_for_display(candidate_data.get('IssueDate', '')),
            'certificate_code': candidate_data.get('CertificateCode', ''),
            'email': candidate_data.get('Email', '')
        }

# Example usage
if __name__ == "__main__":
    generator = CertificateGenerator()
    
    # Test certificate generation
    test_candidate = {
        'Name': 'John Doe',
        'Project': 'AI Chatbot Development',
        'StartDate': 'Jan 15 th 2025',
        'EndDate': 'Apr 15 th 2025',
        'IssueDate': '20-APR-2025',
        'CertificateCode': 'IDA-IN/25/Q1/01/0001',
        'Email': '<EMAIL>'
    }
    
    html = generator.generate_certificate_html(test_candidate)
    print("Certificate HTML generated successfully!")
    print(f"HTML length: {len(html)} characters")
